"use strict";var g=Object.create;var n=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var R=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty;var D=(e,o)=>{for(var r in o)n(e,r,{get:o[r],enumerable:!0})},i=(e,o,r,p)=>{if(o&&typeof o=="object"||typeof o=="function")for(let t of x(o))!h.call(e,t)&&t!==r&&n(e,t,{get:()=>o[t],enumerable:!(p=u(o,t))||p.enumerable});return e};var a=(e,o,r)=>(r=e!=null?g(R(e)):{},i(o||!e||!e.__esModule?n(r,"default",{value:e,enumerable:!0}):r,e)),E=e=>i(n({},"__esModule",{value:!0}),e);var N={};D(N,{Skeleton:()=>m});module.exports=E(N);var s=a(require("react")),l=a(require("classnames")),f=require("radix-ui"),P=require("../helpers/inert.js"),d=require("../helpers/extract-props.js"),k=require("../props/margin.props.js"),c=require("./skeleton.props.js");const m=s.forwardRef((e,o)=>{const{children:r,className:p,loading:t,...S}=(0,d.extractProps)(e,c.skeletonPropDefs,k.marginPropDefs);if(!t)return r;const y=s.isValidElement(r)?f.Slot.Root:"span";return s.createElement(y,{ref:o,"aria-hidden":!0,className:(0,l.default)("rt-Skeleton",p),"data-inline-skeleton":s.isValidElement(r)?void 0:!0,tabIndex:-1,inert:P.inert,...S},r)});m.displayName="Skeleton";
//# sourceMappingURL=skeleton.js.map
