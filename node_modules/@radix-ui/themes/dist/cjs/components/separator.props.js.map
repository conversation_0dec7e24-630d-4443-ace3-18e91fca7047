{"version": 3, "sources": ["../../../src/components/separator.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst orientationValues = ['horizontal', 'vertical'] as const;\nconst sizes = ['1', '2', '3', '4'] as const;\n\nconst separatorPropDefs = {\n  orientation: {\n    type: 'enum',\n    className: 'rt-r-orientation',\n    values: orientationValues,\n    default: 'horizontal',\n    responsive: true,\n  },\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '1', responsive: true },\n  color: { ...colorPropDef.color, default: 'gray' },\n  decorative: { type: 'boolean', default: true },\n} satisfies {\n  orientation: PropDef<(typeof orientationValues)[number]>;\n  size: PropDef<(typeof sizes)[number]>;\n  color: typeof colorPropDef.color;\n  decorative: PropDef<boolean>;\n};\n\nexport { separatorPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,uBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA6B,kCAI7B,MAAMC,EAAoB,CAAC,aAAc,UAAU,EAC7CC,EAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAE3BJ,EAAoB,CACxB,YAAa,CACX,KAAM,OACN,UAAW,mBACX,OAAQG,EACR,QAAS,aACT,WAAY,EACd,EACA,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQC,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,MAAO,CAAE,GAAG,eAAa,MAAO,QAAS,MAAO,EAChD,WAAY,CAAE,KAAM,UAAW,QAAS,EAAK,CAC/C", "names": ["separator_props_exports", "__export", "separatorPropDefs", "__toCommonJS", "import_color_prop", "orientationValues", "sizes"]}