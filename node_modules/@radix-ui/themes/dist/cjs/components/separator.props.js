"use strict";var s=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var l=Object.getOwnPropertyNames;var f=Object.prototype.hasOwnProperty;var c=(e,o)=>{for(var t in o)s(e,t,{get:o[t],enumerable:!0})},u=(e,o,t,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let r of l(o))!f.call(e,r)&&r!==t&&s(e,r,{get:()=>o[r],enumerable:!(a=p(o,r))||a.enumerable});return e};var m=e=>u(s({},"__esModule",{value:!0}),e);var v={};c(v,{separatorPropDefs:()=>n});module.exports=m(v);var i=require("../props/color.prop.js");const y=["horizontal","vertical"],d=["1","2","3","4"],n={orientation:{type:"enum",className:"rt-r-orientation",values:y,default:"horizontal",responsive:!0},size:{type:"enum",className:"rt-r-size",values:d,default:"1",responsive:!0},color:{...i.colorPropDef.color,default:"gray"},decorative:{type:"boolean",default:!0}};
//# sourceMappingURL=separator.props.js.map
