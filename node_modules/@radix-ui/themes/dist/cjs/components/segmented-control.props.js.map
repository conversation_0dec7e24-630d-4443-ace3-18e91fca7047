{"version": 3, "sources": ["../../../src/components/segmented-control.props.tsx"], "sourcesContent": ["import { radiusPropDef } from '../props/radius.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['surface', 'classic'] as const;\n\nconst segmentedControlRootPropDefs = {\n  disabled: { type: 'boolean', className: 'disabled', default: false },\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },\n  ...radiusPropDef,\n} satisfies {\n  disabled?: PropDef<boolean>;\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n};\n\nexport { segmentedControlRootPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,kCAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA8B,mCAI9B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,SAAS,EAEhCJ,EAA+B,CACnC,SAAU,CAAE,KAAM,UAAW,UAAW,WAAY,QAAS,EAAM,EACnE,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQG,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQC,EAAU,QAAS,SAAU,EACvF,GAAG,eACL", "names": ["segmented_control_props_exports", "__export", "segmentedControlRootPropDefs", "__toCommonJS", "import_radius_prop", "sizes", "variants"]}