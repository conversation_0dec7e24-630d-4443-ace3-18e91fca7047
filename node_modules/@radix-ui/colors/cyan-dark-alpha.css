.dark, .dark-theme {
  --cyan-a1: #0091f70a;
  --cyan-a2: #02a7f211;
  --cyan-a3: #00befd28;
  --cyan-a4: #00baff3b;
  --cyan-a5: #00befd4d;
  --cyan-a6: #00c7fd5e;
  --cyan-a7: #14cdff75;
  --cyan-a8: #11cfff95;
  --cyan-a9: #00cfffc3;
  --cyan-a10: #28d6ffcd;
  --cyan-a11: #52e1fee5;
  --cyan-a12: #bbf3fef7;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --cyan-a1: color(display-p3 0 0.647 0.992 / 0.034);
      --cyan-a2: color(display-p3 0.133 0.733 1 / 0.059);
      --cyan-a3: color(display-p3 0.122 0.741 0.996 / 0.152);
      --cyan-a4: color(display-p3 0.051 0.725 1 / 0.227);
      --cyan-a5: color(display-p3 0.149 0.757 1 / 0.29);
      --cyan-a6: color(display-p3 0.267 0.792 1 / 0.358);
      --cyan-a7: color(display-p3 0.333 0.808 1 / 0.446);
      --cyan-a8: color(display-p3 0.357 0.816 1 / 0.572);
      --cyan-a9: color(display-p3 0.357 0.82 1 / 0.748);
      --cyan-a10: color(display-p3 0.4 0.839 1 / 0.786);
      --cyan-a11: color(display-p3 0.446 0.79 0.887);
      --cyan-a12: color(display-p3 0.757 0.919 0.962);
    }
  }
}