:root, .light, .light-theme {
  --lime-a1: #66990005;
  --lime-a2: #6b95000c;
  --lime-a3: #96c80029;
  --lime-a4: #8fc60042;
  --lime-a5: #81bb0059;
  --lime-a6: #72aa006e;
  --lime-a7: #61990087;
  --lime-a8: #559200ab;
  --lime-a9: #93e4009c;
  --lime-a10: #8fdc00b3;
  --lime-a11: #375f00d0;
  --lime-a12: #1e2900e3;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --lime-a1: color(display-p3 0.412 0.608 0.02 / 0.02);
      --lime-a2: color(display-p3 0.514 0.592 0.024 / 0.048);
      --lime-a3: color(display-p3 0.584 0.765 0.008 / 0.15);
      --lime-a4: color(display-p3 0.561 0.757 0.004 / 0.24);
      --lime-a5: color(display-p3 0.514 0.698 0.004 / 0.322);
      --lime-a6: color(display-p3 0.443 0.627 0 / 0.4);
      --lime-a7: color(display-p3 0.376 0.561 0.004 / 0.491);
      --lime-a8: color(display-p3 0.333 0.529 0 / 0.624);
      --lime-a9: color(display-p3 0.588 0.867 0 / 0.534);
      --lime-a10: color(display-p3 0.561 0.827 0 / 0.604);
      --lime-a11: color(display-p3 0.386 0.482 0.227);
      --lime-a12: color(display-p3 0.222 0.25 0.128);
    }
  }
}