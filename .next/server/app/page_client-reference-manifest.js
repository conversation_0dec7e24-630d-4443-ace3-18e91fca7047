globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@radix-ui/react-accordion/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-avatar/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-form/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-menubar/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-popover/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-progress/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-slider/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toggle/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/select.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"*":{"id":"(ssr)/./node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/deals/register/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/deals/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-accordion/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-avatar/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-dialog/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-form/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-label/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-menubar/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-popover/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-portal/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-progress/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-select/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-slider/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-switch/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-tabs/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-toast/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-toggle/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/select.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"id":"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/src/app/(dashboard)/deals/register/page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/deals/register/page.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/gemini project/deal-registration-system/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/home/<USER>/Documents/gemini project/deal-registration-system/src/":[],"/home/<USER>/Documents/gemini project/deal-registration-system/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/home/<USER>/Documents/gemini project/deal-registration-system/src/app/page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@radix-ui/react-accordion/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-avatar/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-form/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-menubar/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-popover/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-progress/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-slider/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toggle/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/select.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"*":{"id":"(rsc)/./node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/deals/register/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/deals/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}