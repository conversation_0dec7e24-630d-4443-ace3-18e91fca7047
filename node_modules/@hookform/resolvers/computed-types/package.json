{"name": "@hookform/resolvers/computed-types", "amdName": "hookformResolversComputedTypes", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: computed-types", "main": "dist/computed-types.js", "module": "dist/computed-types.module.js", "umd:main": "dist/computed-types.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.55.0", "@hookform/resolvers": "^2.0.0"}}