:root, .light, .light-theme {
  --lime-1: #fcfdfa;
  --lime-2: #f8faf3;
  --lime-3: #eef6d6;
  --lime-4: #e2f0bd;
  --lime-5: #d3e7a6;
  --lime-6: #c2da91;
  --lime-7: #abc978;
  --lime-8: #8db654;
  --lime-9: #bdee63;
  --lime-10: #b0e64c;
  --lime-11: #5c7c2f;
  --lime-12: #37401c;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --lime-1: color(display-p3 0.989 0.992 0.981);
      --lime-2: color(display-p3 0.975 0.98 0.954);
      --lime-3: color(display-p3 0.939 0.965 0.851);
      --lime-4: color(display-p3 0.896 0.94 0.76);
      --lime-5: color(display-p3 0.843 0.903 0.678);
      --lime-6: color(display-p3 0.778 0.852 0.599);
      --lime-7: color(display-p3 0.694 0.784 0.508);
      --lime-8: color(display-p3 0.585 0.707 0.378);
      --lime-9: color(display-p3 0.78 0.928 0.466);
      --lime-10: color(display-p3 0.734 0.896 0.397);
      --lime-11: color(display-p3 0.386 0.482 0.227);
      --lime-12: color(display-p3 0.222 0.25 0.128);
    }
  }
}