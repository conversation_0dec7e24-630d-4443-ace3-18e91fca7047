{"version": 3, "sources": ["../../../src/components/segmented-control.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ToggleGroup as ToggleGroupPrimitive } from 'radix-ui';\nimport { useControllableState } from 'radix-ui/internal';\n\nimport { segmentedControlRootPropDefs } from './segmented-control.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype SegmentedControlRootOwnProps = GetPropDefTypes<typeof segmentedControlRootPropDefs>;\n\ninterface SegmentedControlRootProps\n  extends ComponentPropsWithout<'div', RemovedProps | 'dir'>,\n    SegmentedControlRootOwnProps,\n    MarginProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n}\n\nconst SegmentedControlRoot = React.forwardRef<HTMLDivElement, SegmentedControlRootProps>(\n  (props, forwardedRef) => {\n    const {\n      className,\n      children,\n      radius,\n      value: valueProp,\n      defaultValue: defaultValueProp,\n      onValueChange: onValueChangeProp,\n      ...rootProps\n    } = extractProps(props, segmentedControlRootPropDefs, marginPropDefs);\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChangeProp,\n      defaultProp: defaultValueProp,\n    });\n\n    return (\n      <ToggleGroupPrimitive.Root\n        data-disabled={props.disabled || undefined}\n        data-radius={radius}\n        ref={forwardedRef}\n        className={classNames('rt-SegmentedControlRoot', className)}\n        onValueChange={(value) => {\n          if (value) {\n            setValue(value);\n          }\n        }}\n        {...rootProps}\n        type=\"single\"\n        value={value}\n        asChild={false}\n        disabled={!!props.disabled}\n      >\n        {children}\n        <div className=\"rt-SegmentedControlIndicator\" />\n      </ToggleGroupPrimitive.Root>\n    );\n  }\n);\n\nSegmentedControlRoot.displayName = 'SegmentedControl.Root';\n\ninterface SegmentedControlItemOwnProps {\n  value: string;\n}\n\ninterface SegmentedControlItemProps\n  extends ComponentPropsWithout<\n      typeof ToggleGroupPrimitive.Item,\n      RemovedProps | 'disabled' | 'type' | 'value'\n    >,\n    SegmentedControlItemOwnProps {}\n\nconst SegmentedControlItem = React.forwardRef<HTMLButtonElement, SegmentedControlItemProps>(\n  ({ children, className, ...props }, forwardedRef) => (\n    <ToggleGroupPrimitive.Item\n      ref={forwardedRef}\n      className={classNames('rt-reset', 'rt-SegmentedControlItem', className)}\n      {...props}\n      disabled={false}\n      asChild={false}\n    >\n      <span className=\"rt-SegmentedControlItemSeparator\" />\n      <span className=\"rt-SegmentedControlItemLabel\">\n        <span className=\"rt-SegmentedControlItemLabelActive\">{children}</span>\n        <span className=\"rt-SegmentedControlItemLabelInactive\" aria-hidden>\n          {children}\n        </span>\n      </span>\n    </ToggleGroupPrimitive.Item>\n  )\n);\n\nSegmentedControlItem.displayName = 'SegmentedControl.Item';\n\nexport { SegmentedControlRoot as Root, SegmentedControlItem as Item };\nexport type { SegmentedControlRootProps as RootProps, SegmentedControlItemProps as ItemProps };\n"], "mappings": "ukBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,UAAAE,EAAA,SAAAC,IAAA,eAAAC,EAAAJ,GAEA,IAAAK,EAAuB,oBACvBC,EAAuB,yBACvBC,EAAoD,oBACpDC,EAAqC,6BAErCC,EAA6C,wCAC7CC,EAA6B,uCAC7BC,EAA+B,oCAiB/B,MAAMR,EAAuBE,EAAM,WACjC,CAACO,EAAOC,IAAiB,CACvB,KAAM,CACJ,UAAAC,EACA,SAAAC,EACA,OAAAC,EACA,MAAOC,EACP,aAAcC,EACd,cAAeC,EACf,GAAGC,CACL,KAAI,gBAAaR,EAAO,+BAA8B,gBAAc,EAE9D,CAACS,EAAOC,CAAQ,KAAI,wBAAqB,CAC7C,KAAML,EACN,SAAUE,EACV,YAAaD,CACf,CAAC,EAED,OACEb,EAAA,cAAC,EAAAkB,YAAqB,KAArB,CACC,gBAAeX,EAAM,UAAY,OACjC,cAAaI,EACb,IAAKH,EACL,aAAW,EAAAW,SAAW,0BAA2BV,CAAS,EAC1D,cAAgBO,GAAU,CACpBA,GACFC,EAASD,CAAK,CAElB,EACC,GAAGD,EACJ,KAAK,SACL,MAAOC,EACP,QAAS,GACT,SAAU,CAAC,CAACT,EAAM,UAEjBG,EACDV,EAAA,cAAC,OAAI,UAAU,+BAA+B,CAChD,CAEJ,CACF,EAEAF,EAAqB,YAAc,wBAanC,MAAMD,EAAuBG,EAAM,WACjC,CAAC,CAAE,SAAAU,EAAU,UAAAD,EAAW,GAAGF,CAAM,EAAGC,IAClCR,EAAA,cAAC,EAAAkB,YAAqB,KAArB,CACC,IAAKV,EACL,aAAW,EAAAW,SAAW,WAAY,0BAA2BV,CAAS,EACrE,GAAGF,EACJ,SAAU,GACV,QAAS,IAETP,EAAA,cAAC,QAAK,UAAU,mCAAmC,EACnDA,EAAA,cAAC,QAAK,UAAU,gCACdA,EAAA,cAAC,QAAK,UAAU,sCAAsCU,CAAS,EAC/DV,EAAA,cAAC,QAAK,UAAU,uCAAuC,cAAW,IAC/DU,CACH,CACF,CACF,CAEJ,EAEAb,EAAqB,YAAc", "names": ["segmented_control_exports", "__export", "SegmentedControlItem", "SegmentedControlRoot", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_internal", "import_segmented_control_props", "import_extract_props", "import_margin_props", "props", "forwardedRef", "className", "children", "radius", "valueProp", "defaultValueProp", "onValueChangeProp", "rootProps", "value", "setValue", "ToggleGroupPrimitive", "classNames"]}