"use strict";var t=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var l=(e,s)=>{for(var o in s)t(e,o,{get:s[o],enumerable:!0})},v=(e,s,o,a)=>{if(s&&typeof s=="object"||typeof s=="function")for(let r of c(s))!u.call(e,r)&&r!==o&&t(e,r,{get:()=>s[r],enumerable:!(a=m(s,r))||a.enumerable});return e};var D=e=>v(t({},"__esModule",{value:!0}),e);var y={};l(y,{sliderPropDefs:()=>n});module.exports=D(y);var p=require("../props/color.prop.js"),i=require("../props/high-contrast.prop.js"),f=require("../props/radius.prop.js");const P=["1","2","3"],d=["classic","surface","soft"],n={size:{type:"enum",className:"rt-r-size",values:P,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:d,default:"surface"},...p.colorPropDef,...i.highContrastPropDef,...f.radiusPropDef};
//# sourceMappingURL=slider.props.js.map
