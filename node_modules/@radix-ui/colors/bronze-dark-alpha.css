.dark, .dark-theme {
  --bronze-a1: #d1110004;
  --bronze-a2: #fbbc910c;
  --bronze-a3: #faceb817;
  --bronze-a4: #facdb622;
  --bronze-a5: #ffd2c12d;
  --bronze-a6: #ffd1c03c;
  --bronze-a7: #fdd0c04f;
  --bronze-a8: #ffd6c565;
  --bronze-a9: #fec7b09b;
  --bronze-a10: #fecab5a9;
  --bronze-a11: #ffd7c6d1;
  --bronze-a12: #fff1e9ec;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --bronze-a1: color(display-p3 0.941 0.067 0 / 0.009);
      --bronze-a2: color(display-p3 0.98 0.8 0.706 / 0.043);
      --bronze-a3: color(display-p3 0.988 0.851 0.761 / 0.085);
      --bronze-a4: color(display-p3 0.996 0.839 0.78 / 0.127);
      --bronze-a5: color(display-p3 0.996 0.863 0.773 / 0.173);
      --bronze-a6: color(display-p3 1 0.863 0.796 / 0.227);
      --bronze-a7: color(display-p3 1 0.867 0.8 / 0.295);
      --bronze-a8: color(display-p3 1 0.859 0.788 / 0.387);
      --bronze-a9: color(display-p3 1 0.82 0.733 / 0.585);
      --bronze-a10: color(display-p3 1 0.839 0.761 / 0.635);
      --bronze-a11: color(display-p3 0.81 0.707 0.655);
      --bronze-a12: color(display-p3 0.921 0.88 0.854);
    }
  }
}