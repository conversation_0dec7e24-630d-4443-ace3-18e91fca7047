import * as React from 'react';
import { Slider as SliderPrimitive } from 'radix-ui';
import { sliderPropDefs } from './slider.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type SliderOwnProps = GetPropDefTypes<typeof sliderPropDefs>;
interface SliderProps extends ComponentPropsWithout<typeof SliderPrimitive.Root, 'asChild' | 'color' | 'children' | 'defaultChecked'>, MarginProps, SliderOwnProps {
}
declare const Slider: React.ForwardRefExoticComponent<SliderProps & React.RefAttributes<HTMLSpanElement>>;
export { Slider };
export type { SliderProps };
//# sourceMappingURL=slider.d.ts.map