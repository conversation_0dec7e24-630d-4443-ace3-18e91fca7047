"use strict";var y=Object.create;var d=Object.defineProperty;var v=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var N=Object.getPrototypeOf,R=Object.prototype.hasOwnProperty;var T=(e,r)=>{for(var o in r)d(e,o,{get:r[o],enumerable:!0})},f=(e,r,o,s)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of g(r))!R.call(e,i)&&i!==o&&d(e,i,{get:()=>r[i],enumerable:!(s=v(r,i))||s.enumerable});return e};var P=(e,r,o)=>(o=e!=null?y(N(e)):{},f(r||!e||!e.__esModule?d(o,"default",{value:e,enumerable:!0}):o,e)),C=e=>f(d({},"__esModule",{value:!0}),e);var k={};T(k,{Slider:()=>m});module.exports=C(k);var t=P(require("react")),l=P(require("classnames")),p=require("radix-ui"),c=require("./slider.props.js"),S=require("../helpers/extract-props.js"),u=require("../props/margin.props.js");const m=t.forwardRef((e,r)=>{const{className:o,color:s,radius:i,tabIndex:n,...a}=(0,S.extractProps)(e,c.sliderPropDefs,u.marginPropDefs);return t.createElement(p.Slider.Root,{"data-accent-color":s,"data-radius":i,ref:r,...a,asChild:!1,className:(0,l.default)("rt-SliderRoot",o)},t.createElement(p.Slider.Track,{className:"rt-SliderTrack"},t.createElement(p.Slider.Range,{className:(0,l.default)("rt-SliderRange",{"rt-high-contrast":e.highContrast}),"data-inverted":a.inverted?"":void 0})),(a.value??a.defaultValue??[]).map((x,h)=>t.createElement(p.Slider.Thumb,{key:h,className:"rt-SliderThumb",...n!==void 0?{tabIndex:n}:void 0})))});m.displayName="Slider";
//# sourceMappingURL=slider.js.map
