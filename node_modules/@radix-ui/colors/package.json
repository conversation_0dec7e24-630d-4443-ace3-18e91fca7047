{"name": "@radix-ui/colors", "version": "3.0.0", "contributors": ["Colm Tuite <<EMAIL>>", "<PERSON> <<EMAIL>>"], "main": "index.js", "module": "index.mjs", "types": "types/index.d.ts", "scripts": {"build": "yarn clean && yarn && rollup -c && yarn build-css-modules", "build-css-modules": "node ./scripts/build-css-modules.js", "prepublishOnly": "yarn build", "postpublish": "yarn clean", "clean": "git clean -fdX"}, "keywords": ["radix", "colors"], "sideEffects": false, "publishConfig": {"access": "public"}, "devDependencies": {"@rollup/plugin-typescript": "^8.2.1", "@types/node": "^15.0.3", "rollup": "^2.48.0", "tslib": "^2.2.0", "typescript": "^4.2.4"}, "license": "MIT"}