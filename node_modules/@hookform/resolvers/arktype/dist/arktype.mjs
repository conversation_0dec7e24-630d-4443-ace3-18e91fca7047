import{toNestErrors as e,validateFieldsNatively as r}from"@hookform/resolvers";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},t.apply(null,arguments)}function n(e){if(e.path?.length){let r="";for(const t of e.path){const e="object"==typeof t?t.key:t;if("string"!=typeof e&&"number"!=typeof e)return null;r+=r?`.${e}`:e}return r}return null}function o(o,s,i){return void 0===i&&(i={}),function(s,a,u){try{var l=function(){if(f.issues){var o=function(e,r){for(var o={},s=0;s<e.length;s++){var i=e[s],a=n(i);if(a&&(o[a]||(o[a]={message:i.message,type:""}),r)){var u,l=o[a].types||{};o[a].types=t({},l,((u={})[Object.keys(l).length]=i.message,u))}}return o}(f.issues,!u.shouldUseNativeValidation&&"all"===u.criteriaMode);return{values:{},errors:e(o,u)}}return u.shouldUseNativeValidation&&r({},u),{values:i.raw?Object.assign({},s):f.value,errors:{}}},f=o["~standard"].validate(s),c=function(){if(f instanceof Promise)return Promise.resolve(f).then(function(e){f=e})}();return Promise.resolve(c&&c.then?c.then(l):l())}catch(e){return Promise.reject(e)}}}export{o as arktypeResolver};
//# sourceMappingURL=arktype.module.js.map
