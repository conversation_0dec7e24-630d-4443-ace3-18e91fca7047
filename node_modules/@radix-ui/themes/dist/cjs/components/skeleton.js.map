{"version": 3, "sources": ["../../../src/components/skeleton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slot } from 'radix-ui';\n\nimport { inert } from '../helpers/inert.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { skeletonPropDefs } from './skeleton.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\ntype SkeletonElement = React.ElementRef<'span'>;\ntype SkeletonOwnProps = GetPropDefTypes<typeof skeletonPropDefs>;\ninterface SkeletonProps\n  extends ComponentPropsWithout<'span', RemovedProps>,\n    MarginProps,\n    SkeletonOwnProps {}\nconst Skeleton = React.forwardRef<SkeletonElement, SkeletonProps>((props, forwardedRef) => {\n  const { children, className, loading, ...skeletonProps } = extractProps(\n    props,\n    skeletonPropDefs,\n    marginPropDefs\n  );\n\n  if (!loading) return children;\n\n  const Tag = React.isValidElement(children) ? Slot.Root : 'span';\n\n  return (\n    <Tag\n      ref={forwardedRef}\n      aria-hidden\n      className={classNames('rt-Skeleton', className)}\n      data-inline-skeleton={React.isValidElement(children) ? undefined : true}\n      tabIndex={-1}\n      // @ts-expect-error\n      inert={inert}\n      {...skeletonProps}\n    >\n      {children}\n    </Tag>\n  );\n});\nSkeleton.displayName = 'Skeleton';\n\nexport { Skeleton };\nexport type { SkeletonProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,cAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBACvBC,EAAqB,oBAErBC,EAAsB,+BACtBC,EAA6B,uCAC7BC,EAA+B,oCAC/BC,EAAiC,+BAYjC,MAAMR,EAAWE,EAAM,WAA2C,CAACO,EAAOC,IAAiB,CACzF,KAAM,CAAE,SAAAC,EAAU,UAAAC,EAAW,QAAAC,EAAS,GAAGC,CAAc,KAAI,gBACzDL,EACA,mBACA,gBACF,EAEA,GAAI,CAACI,EAAS,OAAOF,EAErB,MAAMI,EAAMb,EAAM,eAAeS,CAAQ,EAAI,OAAK,KAAO,OAEzD,OACET,EAAA,cAACa,EAAA,CACC,IAAKL,EACL,cAAW,GACX,aAAW,EAAAM,SAAW,cAAeJ,CAAS,EAC9C,uBAAsBV,EAAM,eAAeS,CAAQ,EAAI,OAAY,GACnE,SAAU,GAEV,MAAO,QACN,GAAGG,GAEHH,CACH,CAEJ,CAAC,EACDX,EAAS,YAAc", "names": ["skeleton_exports", "__export", "Skeleton", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_inert", "import_extract_props", "import_margin_props", "import_skeleton_props", "props", "forwardedRef", "children", "className", "loading", "skeletonProps", "Tag", "classNames"]}