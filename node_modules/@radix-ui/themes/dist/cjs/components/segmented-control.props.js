"use strict";var r=Object.defineProperty;var n=Object.getOwnPropertyDescriptor;var f=Object.getOwnPropertyNames;var l=Object.prototype.hasOwnProperty;var u=(s,e)=>{for(var o in e)r(s,o,{get:e[o],enumerable:!0})},c=(s,e,o,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of f(e))!l.call(s,a)&&a!==o&&r(s,a,{get:()=>e[a],enumerable:!(t=n(e,a))||t.enumerable});return s};var d=s=>c(r({},"__esModule",{value:!0}),s);var b={};u(b,{segmentedControlRootPropDefs:()=>p});module.exports=d(b);var i=require("../props/radius.prop.js");const m=["1","2","3"],v=["surface","classic"],p={disabled:{type:"boolean",className:"disabled",default:!1},size:{type:"enum",className:"rt-r-size",values:m,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:v,default:"surface"},...i.radiusPropDef};
//# sourceMappingURL=segmented-control.props.js.map
