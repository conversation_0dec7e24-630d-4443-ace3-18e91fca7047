.dark, .dark-theme {
  --violet-a1: #4422ff0f;
  --violet-a2: #853ff916;
  --violet-a3: #8354fe36;
  --violet-a4: #7d51fd50;
  --violet-a5: #845ffd5f;
  --violet-a6: #8f6cfd6d;
  --violet-a7: #9879ff83;
  --violet-a8: #977dfea8;
  --violet-a9: #8668ffcc;
  --violet-a10: #9176fed7;
  --violet-a11: #baa7ff;
  --violet-a12: #e3defffe;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --violet-a1: color(display-p3 0.282 0.141 0.996 / 0.055);
      --violet-a2: color(display-p3 0.51 0.263 1 / 0.08);
      --violet-a3: color(display-p3 0.494 0.337 0.996 / 0.202);
      --violet-a4: color(display-p3 0.49 0.345 1 / 0.299);
      --violet-a5: color(display-p3 0.525 0.392 1 / 0.353);
      --violet-a6: color(display-p3 0.569 0.455 1 / 0.408);
      --violet-a7: color(display-p3 0.588 0.494 1 / 0.496);
      --violet-a8: color(display-p3 0.596 0.51 1 / 0.631);
      --violet-a9: color(display-p3 0.522 0.424 1 / 0.769);
      --violet-a10: color(display-p3 0.576 0.482 1 / 0.811);
      --violet-a11: color(display-p3 0.72 0.65 1);
      --violet-a12: color(display-p3 0.883 0.867 0.986);
    }
  }
}