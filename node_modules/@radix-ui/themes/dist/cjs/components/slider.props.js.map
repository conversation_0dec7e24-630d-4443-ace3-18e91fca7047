{"version": 3, "sources": ["../../../src/components/slider.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\nimport { highContrastPropDef } from '../props/high-contrast.prop.js';\nimport { radiusPropDef } from '../props/radius.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\nconst variants = ['classic', 'surface', 'soft'] as const;\n\nconst sliderPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },\n  ...colorPropDef,\n  ...highContrastPropDef,\n  ...radiusPropDef,\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  variant: PropDef<(typeof variants)[number]>;\n};\n\nexport { sliderPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,oBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA6B,kCAC7BC,EAAoC,0CACpCC,EAA8B,mCAI9B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EACtBC,EAAW,CAAC,UAAW,UAAW,MAAM,EAExCN,EAAiB,CACrB,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQK,EAAO,QAAS,IAAK,WAAY,EAAK,EAC5F,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQC,EAAU,QAAS,SAAU,EACvF,GAAG,eACH,GAAG,sBACH,GAAG,eACL", "names": ["slider_props_exports", "__export", "sliderPropDefs", "__toCommonJS", "import_color_prop", "import_high_contrast_prop", "import_radius_prop", "sizes", "variants"]}