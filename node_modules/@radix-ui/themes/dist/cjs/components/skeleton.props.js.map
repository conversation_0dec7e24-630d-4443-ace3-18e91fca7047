{"version": 3, "sources": ["../../../src/components/skeleton.props.tsx"], "sourcesContent": ["import { heightPropDefs } from '../props/height.props.js';\nimport { widthPropDefs } from '../props/width.props.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst skeletonPropDefs = {\n  loading: { type: 'boolean', default: true },\n  ...widthPropDefs,\n  ...heightPropDefs,\n} satisfies {\n  loading: PropDef<boolean>;\n};\n\nexport { skeletonPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,sBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA+B,oCAC/BC,EAA8B,mCAI9B,MAAMH,EAAmB,CACvB,QAAS,CAAE,KAAM,UAAW,QAAS,EAAK,EAC1C,GAAG,gBACH,GAAG,gBACL", "names": ["skeleton_props_exports", "__export", "skeletonPropDefs", "__toCommonJS", "import_height_props", "import_width_props"]}