{"version": 3, "sources": ["../../../src/components/slider.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slider as SliderPrimitive } from 'radix-ui';\n\nimport { sliderPropDefs } from './slider.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype SliderElement = React.ElementRef<typeof SliderPrimitive.Root>;\ntype SliderOwnProps = GetPropDefTypes<typeof sliderPropDefs>;\ninterface SliderProps\n  extends ComponentPropsWithout<\n      typeof SliderPrimitive.Root,\n      'asChild' | 'color' | 'children' | 'defaultChecked'\n    >,\n    MarginProps,\n    SliderOwnProps {}\nconst Slider = React.forwardRef<SliderElement, SliderProps>((props, forwardedRef) => {\n  const { className, color, radius, tabIndex, ...sliderProps } = extractProps(\n    props,\n    sliderPropDefs,\n    marginPropDefs\n  );\n  return (\n    <SliderPrimitive.Root\n      data-accent-color={color}\n      data-radius={radius}\n      ref={forwardedRef}\n      {...sliderProps}\n      asChild={false}\n      className={classNames('rt-SliderRoot', className)}\n    >\n      <SliderPrimitive.Track className=\"rt-SliderTrack\">\n        <SliderPrimitive.Range\n          className={classNames('rt-SliderRange', { 'rt-high-contrast': props.highContrast })}\n          data-inverted={sliderProps.inverted ? '' : undefined}\n        />\n      </SliderPrimitive.Track>\n      {(sliderProps.value ?? sliderProps.defaultValue ?? []).map((value, index) => (\n        <SliderPrimitive.Thumb\n          key={index}\n          className=\"rt-SliderThumb\"\n          {...(tabIndex !== undefined ? { tabIndex } : undefined)}\n        />\n      ))}\n    </SliderPrimitive.Root>\n  );\n});\nSlider.displayName = 'Slider';\n\nexport { Slider };\nexport type { SliderProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,YAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBACvBC,EAA0C,oBAE1CC,EAA+B,6BAC/BC,EAA6B,uCAC7BC,EAA+B,oCAe/B,MAAMP,EAASE,EAAM,WAAuC,CAACM,EAAOC,IAAiB,CACnF,KAAM,CAAE,UAAAC,EAAW,MAAAC,EAAO,OAAAC,EAAQ,SAAAC,EAAU,GAAGC,CAAY,KAAI,gBAC7DN,EACA,iBACA,gBACF,EACA,OACEN,EAAA,cAAC,EAAAa,OAAgB,KAAhB,CACC,oBAAmBJ,EACnB,cAAaC,EACb,IAAKH,EACJ,GAAGK,EACJ,QAAS,GACT,aAAW,EAAAE,SAAW,gBAAiBN,CAAS,GAEhDR,EAAA,cAAC,EAAAa,OAAgB,MAAhB,CAAsB,UAAU,kBAC/Bb,EAAA,cAAC,EAAAa,OAAgB,MAAhB,CACC,aAAW,EAAAC,SAAW,iBAAkB,CAAE,mBAAoBR,EAAM,YAAa,CAAC,EAClF,gBAAeM,EAAY,SAAW,GAAK,OAC7C,CACF,GACEA,EAAY,OAASA,EAAY,cAAgB,CAAC,GAAG,IAAI,CAACG,EAAOC,IACjEhB,EAAA,cAAC,EAAAa,OAAgB,MAAhB,CACC,IAAKG,EACL,UAAU,iBACT,GAAIL,IAAa,OAAY,CAAE,SAAAA,CAAS,EAAI,OAC/C,CACD,CACH,CAEJ,CAAC,EACDb,EAAO,YAAc", "names": ["slider_exports", "__export", "Slide<PERSON>", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_slider_props", "import_extract_props", "import_margin_props", "props", "forwardedRef", "className", "color", "radius", "tabIndex", "sliderProps", "SliderPrimitive", "classNames", "value", "index"]}