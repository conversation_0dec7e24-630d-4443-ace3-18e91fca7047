"use strict";"use client";var E=Object.create;var n=Object.defineProperty;var G=Object.getOwnPropertyDescriptor;var L=Object.getOwnPropertyNames;var D=Object.getPrototypeOf,z=Object.prototype.hasOwnProperty;var V=(t,r)=>{for(var o in r)n(t,o,{get:r[o],enumerable:!0})},x=(t,r,o,i)=>{if(r&&typeof r=="object"||typeof r=="function")for(let s of L(r))!z.call(t,s)&&s!==o&&n(t,s,{get:()=>r[s],enumerable:!(i=G(r,s))||i.enumerable});return t};var R=(t,r,o)=>(o=t!=null?E(D(t)):{},x(r||!t||!t.__esModule?n(o,"default",{value:t,enumerable:!0}):o,t)),A=t=>x(n({},"__esModule",{value:!0}),t);var W={};V(W,{Content:()=>y,Group:()=>I,Item:()=>h,Label:()=>T,Root:()=>g,Separator:()=>N,Trigger:()=>v});module.exports=A(W);var e=R(require("react")),c=R(require("classnames")),l=require("radix-ui"),u=require("../helpers/extract-props.js"),b=require("../props/margin.props.js"),m=require("./icons.js"),p=require("./select.props.js"),S=require("./theme.js");const C=e.createContext({}),g=t=>{const{children:r,size:o=p.selectRootPropDefs.size.default,...i}=t;return e.createElement(l.Select.Root,{...i},e.createElement(C.Provider,{value:e.useMemo(()=>({size:o}),[o])},r))};g.displayName="Select.Root";const v=e.forwardRef((t,r)=>{const o=e.useContext(C),{children:i,className:s,color:P,radius:f,placeholder:a,...d}=(0,u.extractProps)({size:o?.size,...t},{size:p.selectRootPropDefs.size},p.selectTriggerPropDefs,b.marginPropDefs);return e.createElement(l.Select.Trigger,{asChild:!0},e.createElement("button",{"data-accent-color":P,"data-radius":f,...d,ref:r,className:(0,c.default)("rt-reset","rt-SelectTrigger",s)},e.createElement("span",{className:"rt-SelectTriggerInner"},e.createElement(l.Select.Value,{placeholder:a},i)),e.createElement(l.Select.Icon,{asChild:!0},e.createElement(m.ChevronDownIcon,{className:"rt-SelectIcon"}))))});v.displayName="Select.Trigger";const y=e.forwardRef((t,r)=>{const o=e.useContext(C),{className:i,children:s,color:P,container:f,...a}=(0,u.extractProps)({size:o?.size,...t},{size:p.selectRootPropDefs.size},p.selectContentPropDefs),d=(0,S.useThemeContext)(),w=P||d.accentColor;return e.createElement(l.Select.Portal,{container:f},e.createElement(S.Theme,{asChild:!0},e.createElement(l.Select.Content,{"data-accent-color":w,sideOffset:4,...a,asChild:!1,ref:r,className:(0,c.default)({"rt-PopperContent":a.position==="popper"},"rt-SelectContent",i)},e.createElement(l.ScrollArea.Root,{type:"auto",className:"rt-ScrollAreaRoot"},e.createElement(l.Select.Viewport,{asChild:!0,className:"rt-SelectViewport"},e.createElement(l.ScrollArea.Viewport,{className:"rt-ScrollAreaViewport",style:{overflowY:void 0}},s)),e.createElement(l.ScrollArea.Scrollbar,{className:"rt-ScrollAreaScrollbar rt-r-size-1",orientation:"vertical"},e.createElement(l.ScrollArea.Thumb,{className:"rt-ScrollAreaThumb"}))))))});y.displayName="Select.Content";const h=e.forwardRef((t,r)=>{const{className:o,children:i,...s}=t;return e.createElement(l.Select.Item,{...s,asChild:!1,ref:r,className:(0,c.default)("rt-SelectItem",o)},e.createElement(l.Select.ItemIndicator,{className:"rt-SelectItemIndicator"},e.createElement(m.ThickCheckIcon,{className:"rt-SelectItemIndicatorIcon"})),e.createElement(l.Select.ItemText,null,i))});h.displayName="Select.Item";const I=e.forwardRef(({className:t,...r},o)=>e.createElement(l.Select.Group,{...r,asChild:!1,ref:o,className:(0,c.default)("rt-SelectGroup",t)}));I.displayName="Select.Group";const T=e.forwardRef(({className:t,...r},o)=>e.createElement(l.Select.Label,{...r,asChild:!1,ref:o,className:(0,c.default)("rt-SelectLabel",t)}));T.displayName="Select.Label";const N=e.forwardRef(({className:t,...r},o)=>e.createElement(l.Select.Separator,{...r,asChild:!1,ref:o,className:(0,c.default)("rt-SelectSeparator",t)}));N.displayName="Select.Separator";
//# sourceMappingURL=select.js.map
