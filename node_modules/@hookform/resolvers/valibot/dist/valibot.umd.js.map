{"version": 3, "file": "valibot.umd.js", "sources": ["../src/valibot.ts"], "sourcesContent": ["import { toNestErrors } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\nimport { getDotPath, safeParseAsync } from 'valibot';\nimport { BaseSchema, BaseSchemaAsync, Config, InferIssue } from 'valibot';\n\nexport function valibotResolver<Input extends FieldValues, Context, Output>(\n  schema: BaseSchema<Input, Output, any> | BaseSchemaAsync<Input, Output, any>,\n  schemaOptions?: Partial<\n    Omit<Config<InferIssue<typeof schema>>, 'abortPipeEarly' | 'skipPipe'>\n  >,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function valibotResolver<Input extends FieldValues, Context, Output>(\n  schema: BaseSchema<Input, Output, any> | BaseSchemaAsync<Input, Output, any>,\n  schemaOptions:\n    | Partial<\n        Omit<Config<InferIssue<typeof schema>>, 'abortPipeEarly' | 'skipPipe'>\n      >\n    | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Valibot schema validation\n * @param {BaseSchema<TFieldValues, TFieldValues, any> | BaseSchemaAsync<TFieldValues, TFieldValues, any>} schema - The Valibot schema to validate against\n * @param {Partial<Omit<Config<InferIssue<typeof schema>>, 'abortPipeEarly' | 'skipPipe'>>} [schemaOptions] - Optional Valibot validation options\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {('sync' | 'async')} [resolverOptions.mode] - Validation mode\n * @param {boolean} [resolverOptions.raw] - If true, returns raw values rather than validated results\n * @returns {Resolver<InferOutput<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = valibot.object({\n *   name: valibot.string().minLength(2),\n *   age: valibot.number().min(18)\n * });\n *\n * useForm({\n *   resolver: valibotResolver(schema)\n * });\n */\nexport function valibotResolver<Input extends FieldValues, Context, Output>(\n  schema: BaseSchema<Input, Output, any> | BaseSchemaAsync<Input, Output, any>,\n  schemaOptions?: Partial<\n    Omit<Config<InferIssue<typeof schema>>, 'abortPipeEarly' | 'skipPipe'>\n  >,\n  resolverOptions: {\n    /**\n     * @default async\n     */\n    mode?: 'sync' | 'async';\n    /**\n     * Return the raw input values rather than the parsed values.\n     * @default false\n     */\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values: Input, _, options) => {\n    // Check if we should validate all field criteria\n    const validateAllFieldCriteria =\n      !options.shouldUseNativeValidation && options.criteriaMode === 'all';\n\n    // Parse values with Valibot schema\n    const result = await safeParseAsync(\n      schema,\n      values,\n      Object.assign({}, schemaOptions, {\n        abortPipeEarly: !validateAllFieldCriteria,\n      }),\n    );\n\n    // If there are issues, return them as errors\n    if (result.issues) {\n      // Create errors object\n      const errors: Record<string, FieldError> = {};\n\n      // Iterate over issues to add them to errors object\n      for (; result.issues.length; ) {\n        const issue = result.issues[0];\n        // Create dot path from issue\n        const path = getDotPath(issue);\n\n        if (path) {\n          // Add first error of path to errors object\n          if (!errors[path]) {\n            errors[path] = { message: issue.message, type: issue.type };\n          }\n\n          // If configured, add all errors of path to errors object\n          if (validateAllFieldCriteria) {\n            const types = errors[path].types;\n            const messages = types && types[issue.type];\n            errors[path] = appendErrors(\n              path,\n              validateAllFieldCriteria,\n              errors,\n              issue.type,\n              messages\n                ? ([] as string[]).concat(\n                    messages as string | string[],\n                    issue.message,\n                  )\n                : issue.message,\n            ) as FieldError;\n          }\n        }\n\n        result.issues.shift();\n      }\n\n      // Return resolver result with errors\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      } as const;\n    }\n\n    // Otherwise, return resolver result with values\n    return {\n      values: resolverOptions.raw\n        ? Object.assign({}, values)\n        : (result.output as FieldValues),\n      errors: {},\n    };\n  };\n}\n"], "names": ["schema", "schemaOptions", "resolverOptions", "values", "_", "options", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "Promise", "resolve", "safeParseAsync", "Object", "assign", "abort<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "result", "issues", "errors", "length", "issue", "path", "getDotPath", "message", "type", "types", "messages", "appendErrors", "concat", "shift", "toNestErrors", "raw", "output", "e", "reject"], "mappings": "ubAoDM,SACJA,EACAC,EAGAC,GAYA,YAZAA,IAAAA,IAAAA,EAUI,CAAA,GAEJ,SAAcC,EAAeC,EAAGC,GAAW,IAEzC,IAAMC,GACHD,EAAQE,2BAAsD,QAAzBF,EAAQG,aAAuB,OAAAC,QAAAC,QAGlDC,EAAAA,eACnBX,EACAG,EACAS,OAAOC,OAAO,CAAE,EAAEZ,EAAe,CAC/Ba,gBAAiBR,MAEpBS,KAAA,SANKC,GASN,GAAIA,EAAOC,OAAQ,CAKjB,IAHA,IAAMC,EAAqC,CAAA,EAGpCF,EAAOC,OAAOE,QAAU,CAC7B,IAAMC,EAAQJ,EAAOC,OAAO,GAEtBI,EAAOC,EAAAA,WAAWF,GAExB,GAAIC,IAEGH,EAAOG,KACVH,EAAOG,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAMJ,EAAMI,OAInDlB,GAA0B,CAC5B,IAAMmB,EAAQP,EAAOG,GAAMI,MACrBC,EAAWD,GAASA,EAAML,EAAMI,MACtCN,EAAOG,GAAQM,EAAAA,aACbN,EACAf,EACAY,EACAE,EAAMI,KACNE,EACK,GAAgBE,OACfF,EACAN,EAAMG,SAERH,EAAMG,QAEd,CAGFP,EAAOC,OAAOY,OAChB,CAGA,MAAO,CACL1B,OAAQ,CAAA,EACRe,OAAQY,EAAAA,aAAaZ,EAAQb,GAEjC,CAGA,MAAO,CACLF,OAAQD,EAAgB6B,IACpBnB,OAAOC,OAAO,CAAA,EAAIV,GACjBa,EAAOgB,OACZd,OAAQ,CAAA,EACR,EACJ,CAAC,MAAAe,UAAAxB,QAAAyB,OAAAD,EACH,CAAA,CAAA"}