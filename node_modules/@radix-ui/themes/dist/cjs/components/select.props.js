"use strict";var o=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var u=Object.getOwnPropertyNames;var D=Object.prototype.hasOwnProperty;var P=(s,e)=>{for(var r in e)o(s,r,{get:e[r],enumerable:!0})},g=(s,e,r,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let t of u(e))!D.call(s,t)&&t!==r&&o(s,t,{get:()=>e[t],enumerable:!(i=m(e,t))||i.enumerable});return s};var v=s=>g(o({},"__esModule",{value:!0}),s);var z={};P(z,{selectContentPropDefs:()=>l,selectRootPropDefs:()=>f,selectTriggerPropDefs:()=>c});module.exports=v(z);var a=require("../props/color.prop.js"),n=require("../props/high-contrast.prop.js"),p=require("../props/radius.prop.js");const d=["1","2","3"],f={size:{type:"enum",className:"rt-r-size",values:d,default:"2",responsive:!0}},y=["classic","surface","soft","ghost"],c={variant:{type:"enum",className:"rt-variant",values:y,default:"surface"},...a.colorPropDef,...p.radiusPropDef,placeholder:{type:"string"}},h=["solid","soft"],l={variant:{type:"enum",className:"rt-variant",values:h,default:"solid"},...a.colorPropDef,...n.highContrastPropDef};
//# sourceMappingURL=select.props.js.map
