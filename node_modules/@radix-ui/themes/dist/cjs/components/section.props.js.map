{"version": 3, "sources": ["../../../src/components/section.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\n\nimport type { PropDef, GetPropDefTypes } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3', '4'] as const;\nconst displayValues = ['none', 'initial'] as const;\n\nconst sectionPropDefs = {\n  ...asChildPropDef,\n  /**\n   * Controls the vertical padding of the section.\n   *\n   * @values\n   * | Size     | Padding |\n   * | :------- | ------: |\n   * | size=\"1\" | 24px    |\n   * | size=\"2\" | 40px    |\n   * | size=\"3\" | 64px    |\n   * | size=\"4\" | 80px    |\n   *\n   * @example\n   * size=\"4\"\n   * size={{ sm: '3', lg: '4' }}\n   *\n   * @link\n   * https://github.com/radix-ui/themes/blob/main/packages/radix-ui-themes/src/components/section.css\n   */\n  size: {\n    type: 'enum',\n    className: 'rt-r-size',\n    values: sizes,\n    default: '3',\n    responsive: true,\n  },\n  /**\n   * Controls whether the section is visible or hidden.\n   * Supports \"none\", \"initial\", and responsive object values.\n   *\n   * @example\n   * display=\"none\"\n   * display={{ sm: 'none', lg: 'initial' }}\n   */\n  display: {\n    type: 'enum',\n    className: 'rt-r-display',\n    values: displayValues,\n    parseValue: parseDisplayValue,\n    responsive: true,\n  },\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n  display: PropDef<(typeof displayValues)[number]>;\n};\n\nfunction parseDisplayValue(value: string) {\n  return value === 'initial' ? 'block' : value;\n}\n\n// Use all of the imported prop defs to ensure that JSDoc works\ntype SectionOwnProps = GetPropDefTypes<typeof sectionPropDefs & typeof asChildPropDef>;\n\nexport { sectionPropDefs };\nexport type { SectionOwnProps };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,qBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA+B,qCAI/B,MAAMC,EAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3BC,EAAgB,CAAC,OAAQ,SAAS,EAElCJ,EAAkB,CACtB,GAAG,iBAmBH,KAAM,CACJ,KAAM,OACN,UAAW,YACX,OAAQG,EACR,QAAS,IACT,WAAY,EACd,EASA,QAAS,CACP,KAAM,OACN,UAAW,eACX,OAAQC,EACR,WAAYC,EACZ,WAAY,EACd,CACF,EAKA,SAASA,EAAkBC,EAAe,CACxC,OAAOA,IAAU,UAAY,QAAUA,CACzC", "names": ["section_props_exports", "__export", "sectionPropDefs", "__toCommonJS", "import_as_child_prop", "sizes", "displayValues", "parseDisplayValue", "value"]}