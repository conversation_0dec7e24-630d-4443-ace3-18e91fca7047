"use strict";"use client";var N=Object.create;var a=Object.defineProperty;var b=Object.getOwnPropertyDescriptor;var T=Object.getOwnPropertyNames;var V=Object.getPrototypeOf,G=Object.prototype.hasOwnProperty;var w=(e,o)=>{for(var t in o)a(e,t,{get:o[t],enumerable:!0})},g=(e,o,t,s)=>{if(o&&typeof o=="object"||typeof o=="function")for(let n of T(o))!G.call(e,n)&&n!==t&&a(e,n,{get:()=>o[n],enumerable:!(s=b(o,n))||s.enumerable});return e};var f=(e,o,t)=>(t=e!=null?N(V(e)):{},g(o||!e||!e.__esModule?a(t,"default",{value:e,enumerable:!0}):t,e)),D=e=>g(a({},"__esModule",{value:!0}),e);var x={};w(x,{Item:()=>d,Root:()=>l});module.exports=D(x);var r=f(require("react")),m=f(require("classnames")),p=require("radix-ui"),P=require("radix-ui/internal"),u=require("./segmented-control.props.js"),C=require("../helpers/extract-props.js"),S=require("../props/margin.props.js");const l=r.forwardRef((e,o)=>{const{className:t,children:s,radius:n,value:v,defaultValue:I,onValueChange:c,...y}=(0,C.extractProps)(e,u.segmentedControlRootPropDefs,S.marginPropDefs),[R,h]=(0,P.useControllableState)({prop:v,onChange:c,defaultProp:I});return r.createElement(p.ToggleGroup.Root,{"data-disabled":e.disabled||void 0,"data-radius":n,ref:o,className:(0,m.default)("rt-SegmentedControlRoot",t),onValueChange:i=>{i&&h(i)},...y,type:"single",value:R,asChild:!1,disabled:!!e.disabled},s,r.createElement("div",{className:"rt-SegmentedControlIndicator"}))});l.displayName="SegmentedControl.Root";const d=r.forwardRef(({children:e,className:o,...t},s)=>r.createElement(p.ToggleGroup.Item,{ref:s,className:(0,m.default)("rt-reset","rt-SegmentedControlItem",o),...t,disabled:!1,asChild:!1},r.createElement("span",{className:"rt-SegmentedControlItemSeparator"}),r.createElement("span",{className:"rt-SegmentedControlItemLabel"},r.createElement("span",{className:"rt-SegmentedControlItemLabelActive"},e),r.createElement("span",{className:"rt-SegmentedControlItemLabelInactive","aria-hidden":!0},e))));d.displayName="SegmentedControl.Item";
//# sourceMappingURL=segmented-control.js.map
