{"name": "@hookform/resolvers/vest", "amdName": "hookformResolversVest", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: vest", "main": "dist/vest.js", "module": "dist/vest.module.js", "umd:main": "dist/vest.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.55.0", "@hookform/resolvers": "^2.0.0", "vest": ">=3.0.0"}}