.dark, .dark-theme {
  --teal-a1: #00deab05;
  --teal-a2: #12fbe60c;
  --teal-a3: #00ffe61e;
  --teal-a4: #00ffe92d;
  --teal-a5: #00ffea3b;
  --teal-a6: #1cffe84b;
  --teal-a7: #2efde85f;
  --teal-a8: #32ffe775;
  --teal-a9: #13ffe49f;
  --teal-a10: #0dffe0ae;
  --teal-a11: #0afed5d6;
  --teal-a12: #b8ffebef;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --teal-a1: color(display-p3 0 0.992 0.761 / 0.017);
      --teal-a2: color(display-p3 0.235 0.988 0.902 / 0.047);
      --teal-a3: color(display-p3 0.235 1 0.898 / 0.118);
      --teal-a4: color(display-p3 0.18 0.996 0.929 / 0.173);
      --teal-a5: color(display-p3 0.31 1 0.933 / 0.227);
      --teal-a6: color(display-p3 0.396 1 0.933 / 0.286);
      --teal-a7: color(display-p3 0.443 1 0.925 / 0.366);
      --teal-a8: color(display-p3 0.459 1 0.925 / 0.454);
      --teal-a9: color(display-p3 0.443 0.996 0.906 / 0.61);
      --teal-a10: color(display-p3 0.439 0.996 0.89 / 0.669);
      --teal-a11: color(display-p3 0.388 0.835 0.719);
      --teal-a12: color(display-p3 0.734 0.934 0.87);
    }
  }
}