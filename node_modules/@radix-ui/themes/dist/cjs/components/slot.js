"use strict";var S=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var s=Object.getOwnPropertyNames;var a=Object.prototype.hasOwnProperty;var c=(t,o)=>{for(var e in o)S(t,e,{get:o[e],enumerable:!0})},m=(t,o,e,i)=>{if(o&&typeof o=="object"||typeof o=="function")for(let l of s(o))!a.call(t,l)&&l!==e&&S(t,l,{get:()=>o[l],enumerable:!(i=p(o,l))||i.enumerable});return t};var n=t=>m(S({},"__esModule",{value:!0}),t);var f={};c(f,{Root:()=>x,Slot:()=>R,Slottable:()=>b});module.exports=n(f);var r=require("radix-ui");const x=r.Slot.Root,R=r.Slot.Root,b=r.Slot.Slottable;
//# sourceMappingURL=slot.js.map
