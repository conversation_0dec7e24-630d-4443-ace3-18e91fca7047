"use strict";var y=Object.create;var a=Object.defineProperty;var S=Object.getOwnPropertyDescriptor;var D=Object.getOwnPropertyNames;var R=Object.getPrototypeOf,g=Object.prototype.hasOwnProperty;var u=(r,o)=>{for(var p in o)a(r,p,{get:o[p],enumerable:!0})},m=(r,o,p,t)=>{if(o&&typeof o=="object"||typeof o=="function")for(let e of D(o))!g.call(r,e)&&e!==p&&a(r,e,{get:()=>o[e],enumerable:!(t=S(o,e))||t.enumerable});return r};var n=(r,o,p)=>(p=r!=null?y(R(r)):{},m(o||!r||!r.__esModule?a(p,"default",{value:r,enumerable:!0}):p,r)),x=r=>m(a({},"__esModule",{value:!0}),r);var N={};u(N,{Separator:()=>s});module.exports=x(N);var f=n(require("react")),i=n(require("classnames")),P=require("./separator.props.js"),c=require("../helpers/extract-props.js"),l=require("../props/margin.props.js");const s=f.forwardRef((r,o)=>{const{className:p,color:t,decorative:e,...d}=(0,c.extractProps)(r,P.separatorPropDefs,l.marginPropDefs);return f.createElement("span",{"data-accent-color":t,role:e?void 0:"separator",...d,ref:o,className:(0,i.default)("rt-Separator",p)})});s.displayName="Separator";
//# sourceMappingURL=separator.js.map
