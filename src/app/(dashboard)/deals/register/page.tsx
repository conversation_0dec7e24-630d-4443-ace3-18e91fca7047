
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Heading, TextField, Button, Flex, Text, Callout } from '@radix-ui/themes';
import { supabase } from '@/lib/supabase';

const dealSchema = z.object({
  customer_name: z.string().min(1, "Customer Name is required"),
  status: z.enum(['pending', 'approved', 'rejected', 'conflict', 'assigned']).default('pending'),
  deal_value: z.preprocess(
    (val) => val === "" ? undefined : parseFloat(z.string().parse(val)),
    z.number().min(0, "Deal Value must be positive").optional()
  ),
  expiration_date: z.string().optional(), // Will handle date parsing later
  notes: z.string().optional(),
});

type DealFormData = z.infer<typeof dealSchema>;

export default function RegisterDealPage() {
  const { register, handleSubmit, formState: { errors, isValid } } = useForm<DealFormData>({
    resolver: zodResolver(dealSchema),
    defaultValues: {
      status: 'pending',
    },
  });

  const [submissionStatus, setSubmissionStatus] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);



  const onSubmit = async (data: DealFormData) => {
    console.log("Submitting deal...");
    setSubmissionStatus(null);
    setIsSubmitting(true);
    try {
      console.log("Form data before Supabase insert:", data);

      // Generate a temporary UUID for reseller_id since authentication is not implemented yet
      // In a real app, this would come from the logged-in user's profile
      const resellerId = "550e8400-e29b-41d4-a716-************"; // Temporary valid UUID

      // Prepare the deal data for insertion
      // Note: customer_name column doesn't exist in deals table, so we'll store it in notes for now
      const dealData = {
        reseller_id: resellerId,
        status: data.status,
        deal_value: data.deal_value || null,
        expiration_date: data.expiration_date ? new Date(data.expiration_date).toISOString() : null,
        notes: data.notes ? `Customer: ${data.customer_name}\n\nNotes: ${data.notes}` : `Customer: ${data.customer_name}`,
      };

      console.log("Deal data to insert:", dealData);

      const { data: newDeal, error } = await supabase
        .from('deals')
        .insert(dealData)
        .select();

      if (error) {
        console.error("Supabase insert error:", error);
        // Provide more helpful error message
        if (error.code === 'PGRST204') {
          throw new Error(`Database schema error: ${error.message}. Please check that the deals table has the correct columns.`);
        }
        throw error;
      }

      console.log('Deal submitted successfully:', newDeal);
      setSubmissionStatus({ type: 'success', message: 'Deal registered successfully!' });

    } catch (error: any) {
      console.error('Error submitting deal in catch block:', error);
      const errorMessage = error?.message || error?.toString() || 'Unknown error occurred';
      setSubmissionStatus({ type: 'error', message: `Error registering deal: ${errorMessage}` });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-8">
      <Heading size="7" mb="6">Register New Deal</Heading>

      {submissionStatus && (
        <Callout.Root color={submissionStatus.type === 'success' ? 'green' : 'red'} mb="4">
          <Callout.Text>{submissionStatus.message}</Callout.Text>
        </Callout.Root>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <Text as="div" size="2" mb="1" weight="bold">Customer Name</Text>
          <TextField.Root
            size="3"
            placeholder="Enter customer company name"
            {...register('customer_name')}
          />
          {errors.customer_name && <Text color="red" size="1">{errors.customer_name.message}</Text>}
        </div>

        <div>
          <Text as="div" size="2" mb="1" weight="bold">Deal Value (Optional)</Text>
          <TextField.Root
            size="3"
            type="number"
            step="0.01"
            placeholder="e.g., 10000.00"
            {...register('deal_value')}
          />
          {errors.deal_value && <Text color="red" size="1">{errors.deal_value.message}</Text>}
        </div>

        <div>
          <Text as="div" size="2" mb="1" weight="bold">Expiration Date (Optional)</Text>
          <TextField.Root
            size="3"
            type="date"
            {...register('expiration_date')}
          />
          {errors.expiration_date && <Text color="red" size="1">{errors.expiration_date.message}</Text>}
        </div>

        <div>
          <Text as="div" size="2" mb="1" weight="bold">Notes (Optional)</Text>
          <TextField.Root
            size="3"
            as="textarea"
            rows={4}
            placeholder="Any additional notes about the deal..."
            {...register('notes')}
          />
          {errors.notes && <Text color="red" size="1">{errors.notes.message}</Text>}
        </div>

        <Button size="3" type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Registering...' : 'Register Deal'}
        </Button>
      </form>
    </div>
  );
}
