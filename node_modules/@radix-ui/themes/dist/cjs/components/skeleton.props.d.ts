declare const skeletonPropDefs: {
    height: {
        type: "string";
        className: string;
        customProperties: "--height"[];
        responsive: true;
    };
    minHeight: {
        type: "string";
        className: string;
        customProperties: "--min-height"[];
        responsive: true;
    };
    maxHeight: {
        type: "string";
        className: string;
        customProperties: "--max-height"[];
        responsive: true;
    };
    width: {
        type: "string";
        className: string;
        customProperties: "--width"[];
        responsive: true;
    };
    minWidth: {
        type: "string";
        className: string;
        customProperties: "--min-width"[];
        responsive: true;
    };
    maxWidth: {
        type: "string";
        className: string;
        customProperties: "--max-width"[];
        responsive: true;
    };
    loading: {
        type: "boolean";
        default: true;
    };
};
export { skeletonPropDefs };
//# sourceMappingURL=skeleton.props.d.ts.map