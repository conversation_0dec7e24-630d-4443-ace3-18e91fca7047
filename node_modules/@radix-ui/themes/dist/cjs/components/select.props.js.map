{"version": 3, "sources": ["../../../src/components/select.props.tsx"], "sourcesContent": ["import { colorPropDef } from '../props/color.prop.js';\nimport { highContrastPropDef } from '../props/high-contrast.prop.js';\nimport { radiusPropDef } from '../props/radius.prop.js';\n\nimport type { PropDef } from '../props/prop-def.js';\n\nconst sizes = ['1', '2', '3'] as const;\n\nconst selectRootPropDefs = {\n  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },\n} satisfies {\n  size: PropDef<(typeof sizes)[number]>;\n};\n\nconst triggerVariants = ['classic', 'surface', 'soft', 'ghost'] as const;\n\nconst selectTriggerPropDefs = {\n  variant: { type: 'enum', className: 'rt-variant', values: triggerVariants, default: 'surface' },\n  ...colorPropDef,\n  ...radiusPropDef,\n  placeholder: { type: 'string' },\n} satisfies {\n  variant: PropDef<(typeof triggerVariants)[number]>;\n  placeholder: PropDef<string>;\n};\n\nconst contentVariants = ['solid', 'soft'] as const;\n\nconst selectContentPropDefs = {\n  variant: { type: 'enum', className: 'rt-variant', values: contentVariants, default: 'solid' },\n  ...colorPropDef,\n  ...highContrastPropDef,\n} satisfies {\n  variant: PropDef<(typeof contentVariants)[number]>;\n};\n\nexport { selectRootPropDefs, selectTriggerPropDefs, selectContentPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,2BAAAE,EAAA,uBAAAC,EAAA,0BAAAC,IAAA,eAAAC,EAAAL,GAAA,IAAAM,EAA6B,kCAC7BC,EAAoC,0CACpCC,EAA8B,mCAI9B,MAAMC,EAAQ,CAAC,IAAK,IAAK,GAAG,EAEtBN,EAAqB,CACzB,KAAM,CAAE,KAAM,OAAQ,UAAW,YAAa,OAAQM,EAAO,QAAS,IAAK,WAAY,EAAK,CAC9F,EAIMC,EAAkB,CAAC,UAAW,UAAW,OAAQ,OAAO,EAExDN,EAAwB,CAC5B,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQM,EAAiB,QAAS,SAAU,EAC9F,GAAG,eACH,GAAG,gBACH,YAAa,CAAE,KAAM,QAAS,CAChC,EAKMC,EAAkB,CAAC,QAAS,MAAM,EAElCT,EAAwB,CAC5B,QAAS,CAAE,KAAM,OAAQ,UAAW,aAAc,OAAQS,EAAiB,QAAS,OAAQ,EAC5F,GAAG,eACH,GAAG,qBACL", "names": ["select_props_exports", "__export", "selectContentPropDefs", "selectRootPropDefs", "selectTriggerPropDefs", "__toCommonJS", "import_color_prop", "import_high_contrast_prop", "import_radius_prop", "sizes", "triggerVariants", "contentVariants"]}