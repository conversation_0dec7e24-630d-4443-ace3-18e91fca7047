"use strict";var t=Object.defineProperty;var l=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var n=Object.prototype.hasOwnProperty;var d=(e,o)=>{for(var r in o)t(e,r,{get:o[r],enumerable:!0})},D=(e,o,r,f)=>{if(o&&typeof o=="object"||typeof o=="function")for(let p of m(o))!n.call(e,p)&&p!==r&&t(e,p,{get:()=>o[p],enumerable:!(f=l(o,p))||f.enumerable});return e};var P=e=>D(t({},"__esModule",{value:!0}),e);var g={};d(g,{skeletonPropDefs:()=>a});module.exports=P(g);var s=require("../props/height.props.js"),i=require("../props/width.props.js");const a={loading:{type:"boolean",default:!0},...i.widthPropDefs,...s.heightPropDefs};
//# sourceMappingURL=skeleton.props.js.map
