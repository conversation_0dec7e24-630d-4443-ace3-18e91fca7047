{"version": 3, "sources": ["../../../src/components/separator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\n\nimport { separatorPropDefs } from './separator.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype SeparatorElement = React.ElementRef<'span'>;\ntype SeparatorOwnProps = GetPropDefTypes<typeof separatorPropDefs>;\ninterface SeparatorProps\n  extends ComponentPropsWithout<'span', RemovedProps>,\n    MarginProps,\n    SeparatorOwnProps {}\nconst Separator = React.forwardRef<SeparatorElement, SeparatorProps>((props, forwardedRef) => {\n  const { className, color, decorative, ...separatorProps } = extractProps(\n    props,\n    separatorPropDefs,\n    marginPropDefs\n  );\n  return (\n    <span\n      data-accent-color={color}\n      role={decorative ? undefined : 'separator'}\n      {...separatorProps}\n      ref={forwardedRef}\n      className={classNames('rt-Separator', className)}\n    />\n  );\n});\nSeparator.displayName = 'Separator';\n\nexport { Separator };\nexport type { SeparatorProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,eAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBAEvBC,EAAkC,gCAClCC,EAA6B,uCAC7BC,EAA+B,oCAY/B,MAAMN,EAAYE,EAAM,WAA6C,CAACK,EAAOC,IAAiB,CAC5F,KAAM,CAAE,UAAAC,EAAW,MAAAC,EAAO,WAAAC,EAAY,GAAGC,CAAe,KAAI,gBAC1DL,EACA,oBACA,gBACF,EACA,OACEL,EAAA,cAAC,QACC,oBAAmBQ,EACnB,KAAMC,EAAa,OAAY,YAC9B,GAAGC,EACJ,IAAKJ,EACL,aAAW,EAAAK,SAAW,eAAgBJ,CAAS,EACjD,CAEJ,CAAC,EACDT,EAAU,YAAc", "names": ["separator_exports", "__export", "Separator", "__toCommonJS", "React", "import_classnames", "import_separator_props", "import_extract_props", "import_margin_props", "props", "forwardedRef", "className", "color", "decorative", "separatorProps", "classNames"]}