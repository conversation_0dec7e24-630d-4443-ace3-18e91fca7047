{"version": 3, "sources": ["../../../src/components/select.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Select as SelectPrimitive, ScrollArea as ScrollAreaPrimitive } from 'radix-ui';\n\nimport { extractProps } from '../helpers/extract-props.js';\nimport { marginPropDefs } from '../props/margin.props.js';\nimport { ChevronDownIcon, ThickCheckIcon } from './icons.js';\nimport {\n  selectRootPropDefs,\n  selectTriggerPropDefs,\n  selectContentPropDefs,\n} from './select.props.js';\nimport { useThemeContext, Theme } from './theme.js';\n\nimport type { MarginProps } from '../props/margin.props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\n\ntype SelectRootOwnProps = GetPropDefTypes<typeof selectRootPropDefs>;\n\ntype SelectContextValue = SelectRootOwnProps;\nconst SelectContext = React.createContext<SelectContextValue>({});\n\ninterface SelectRootProps extends SelectPrimitive.SelectProps, SelectContextValue {}\nconst SelectRoot: React.FC<SelectRootProps> = (props) => {\n  const { children, size = selectRootPropDefs.size.default, ...rootProps } = props;\n  return (\n    <SelectPrimitive.Root {...rootProps}>\n      <SelectContext.Provider value={React.useMemo(() => ({ size }), [size])}>\n        {children}\n      </SelectContext.Provider>\n    </SelectPrimitive.Root>\n  );\n};\nSelectRoot.displayName = 'Select.Root';\n\ntype SelectTriggerElement = React.ElementRef<typeof SelectPrimitive.Trigger>;\ntype SelectTriggerOwnProps = GetPropDefTypes<typeof selectTriggerPropDefs>;\ninterface SelectTriggerProps\n  extends ComponentPropsWithout<typeof SelectPrimitive.Trigger, RemovedProps>,\n    MarginProps,\n    SelectTriggerOwnProps {}\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props, forwardedRef) => {\n    const context = React.useContext(SelectContext);\n    const { children, className, color, radius, placeholder, ...triggerProps } = extractProps(\n      // Pass size value from the context to generate styles\n      { size: context?.size, ...props },\n      // Pass size prop def to allow it to be extracted\n      { size: selectRootPropDefs.size },\n      selectTriggerPropDefs,\n      marginPropDefs\n    );\n    return (\n      <SelectPrimitive.Trigger asChild>\n        <button\n          data-accent-color={color}\n          data-radius={radius}\n          {...triggerProps}\n          ref={forwardedRef}\n          className={classNames('rt-reset', 'rt-SelectTrigger', className)}\n        >\n          <span className=\"rt-SelectTriggerInner\">\n            <SelectPrimitive.Value placeholder={placeholder}>{children}</SelectPrimitive.Value>\n          </span>\n          <SelectPrimitive.Icon asChild>\n            <ChevronDownIcon className=\"rt-SelectIcon\" />\n          </SelectPrimitive.Icon>\n        </button>\n      </SelectPrimitive.Trigger>\n    );\n  }\n);\nSelectTrigger.displayName = 'Select.Trigger';\n\ntype SelectContentElement = React.ElementRef<typeof SelectPrimitive.Content>;\ntype SelectContentOwnProps = GetPropDefTypes<typeof selectContentPropDefs>;\ninterface SelectContentProps\n  extends ComponentPropsWithout<typeof SelectPrimitive.Content, RemovedProps>,\n    SelectContentOwnProps {\n  container?: React.ComponentPropsWithoutRef<typeof SelectPrimitive.Portal>['container'];\n}\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props, forwardedRef) => {\n    const context = React.useContext(SelectContext);\n    const { className, children, color, container, ...contentProps } = extractProps(\n      // Pass size value from the context to generate styles\n      { size: context?.size, ...props },\n      // Pass size prop def to allow it to be extracted\n      { size: selectRootPropDefs.size },\n      selectContentPropDefs\n    );\n    const themeContext = useThemeContext();\n    const resolvedColor = color || themeContext.accentColor;\n    return (\n      <SelectPrimitive.Portal container={container}>\n        <Theme asChild>\n          <SelectPrimitive.Content\n            data-accent-color={resolvedColor}\n            sideOffset={4}\n            {...contentProps}\n            asChild={false}\n            ref={forwardedRef}\n            className={classNames(\n              { 'rt-PopperContent': contentProps.position === 'popper' },\n              'rt-SelectContent',\n              className\n            )}\n          >\n            <ScrollAreaPrimitive.Root type=\"auto\" className=\"rt-ScrollAreaRoot\">\n              <SelectPrimitive.Viewport asChild className=\"rt-SelectViewport\">\n                <ScrollAreaPrimitive.Viewport\n                  className=\"rt-ScrollAreaViewport\"\n                  style={{ overflowY: undefined }}\n                >\n                  {children}\n                </ScrollAreaPrimitive.Viewport>\n              </SelectPrimitive.Viewport>\n              <ScrollAreaPrimitive.Scrollbar\n                className=\"rt-ScrollAreaScrollbar rt-r-size-1\"\n                orientation=\"vertical\"\n              >\n                <ScrollAreaPrimitive.Thumb className=\"rt-ScrollAreaThumb\" />\n              </ScrollAreaPrimitive.Scrollbar>\n            </ScrollAreaPrimitive.Root>\n          </SelectPrimitive.Content>\n        </Theme>\n      </SelectPrimitive.Portal>\n    );\n  }\n);\nSelectContent.displayName = 'Select.Content';\n\ntype SelectItemElement = React.ElementRef<typeof SelectPrimitive.Item>;\ninterface SelectItemProps\n  extends ComponentPropsWithout<typeof SelectPrimitive.Item, RemovedProps> {}\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>((props, forwardedRef) => {\n  const { className, children, ...itemProps } = props;\n  return (\n    <SelectPrimitive.Item\n      {...itemProps}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-SelectItem', className)}\n    >\n      <SelectPrimitive.ItemIndicator className=\"rt-SelectItemIndicator\">\n        <ThickCheckIcon className=\"rt-SelectItemIndicatorIcon\" />\n      </SelectPrimitive.ItemIndicator>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  );\n});\nSelectItem.displayName = 'Select.Item';\n\ntype SelectGroupElement = React.ElementRef<typeof SelectPrimitive.Group>;\ninterface SelectGroupProps\n  extends ComponentPropsWithout<typeof SelectPrimitive.Group, RemovedProps> {}\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <SelectPrimitive.Group\n      {...props}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-SelectGroup', className)}\n    />\n  )\n);\nSelectGroup.displayName = 'Select.Group';\n\ntype SelectLabelElement = React.ElementRef<typeof SelectPrimitive.Label>;\ninterface SelectLabelProps\n  extends ComponentPropsWithout<typeof SelectPrimitive.Label, RemovedProps> {}\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <SelectPrimitive.Label\n      {...props}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-SelectLabel', className)}\n    />\n  )\n);\nSelectLabel.displayName = 'Select.Label';\n\ntype SelectSeparatorElement = React.ElementRef<typeof SelectPrimitive.Separator>;\ninterface SelectSeparatorProps\n  extends ComponentPropsWithout<typeof SelectPrimitive.Separator, RemovedProps> {}\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <SelectPrimitive.Separator\n      {...props}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-SelectSeparator', className)}\n    />\n  )\n);\nSelectSeparator.displayName = 'Select.Separator';\n\nexport {\n  SelectRoot as Root,\n  SelectTrigger as Trigger,\n  SelectContent as Content,\n  SelectItem as Item,\n  SelectGroup as Group,\n  SelectLabel as Label,\n  SelectSeparator as Separator,\n};\n\nexport type {\n  SelectRootProps as RootProps,\n  SelectTriggerProps as TriggerProps,\n  SelectContentProps as ContentProps,\n  SelectItemProps as ItemProps,\n  SelectGroupProps as GroupProps,\n  SelectLabelProps as LabelProps,\n  SelectSeparatorProps as SeparatorProps,\n};\n"], "mappings": "ukBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,EAAA,UAAAC,EAAA,SAAAC,EAAA,UAAAC,EAAA,SAAAC,EAAA,cAAAC,EAAA,YAAAC,IAAA,eAAAC,EAAAT,GAEA,IAAAU,EAAuB,oBACvBC,EAAuB,yBACvBC,EAA6E,oBAE7EC,EAA6B,uCAC7BC,EAA+B,oCAC/BC,EAAgD,sBAChDC,EAIO,6BACPC,EAAuC,sBASvC,MAAMC,EAAgBR,EAAM,cAAkC,CAAC,CAAC,EAG1DJ,EAAyCa,GAAU,CACvD,KAAM,CAAE,SAAAC,EAAU,KAAAC,EAAO,qBAAmB,KAAK,QAAS,GAAGC,CAAU,EAAIH,EAC3E,OACET,EAAA,cAAC,EAAAa,OAAgB,KAAhB,CAAsB,GAAGD,GACxBZ,EAAA,cAACQ,EAAc,SAAd,CAAuB,MAAOR,EAAM,QAAQ,KAAO,CAAE,KAAAW,CAAK,GAAI,CAACA,CAAI,CAAC,GAClED,CACH,CACF,CAEJ,EACAd,EAAW,YAAc,cAQzB,MAAME,EAAgBE,EAAM,WAC1B,CAACS,EAAOK,IAAiB,CACvB,MAAMC,EAAUf,EAAM,WAAWQ,CAAa,EACxC,CAAE,SAAAE,EAAU,UAAAM,EAAW,MAAAC,EAAO,OAAAC,EAAQ,YAAAC,EAAa,GAAGC,CAAa,KAAI,gBAE3E,CAAE,KAAML,GAAS,KAAM,GAAGN,CAAM,EAEhC,CAAE,KAAM,qBAAmB,IAAK,EAChC,wBACA,gBACF,EACA,OACET,EAAA,cAAC,EAAAa,OAAgB,QAAhB,CAAwB,QAAO,IAC9Bb,EAAA,cAAC,UACC,oBAAmBiB,EACnB,cAAaC,EACZ,GAAGE,EACJ,IAAKN,EACL,aAAW,EAAAO,SAAW,WAAY,mBAAoBL,CAAS,GAE/DhB,EAAA,cAAC,QAAK,UAAU,yBACdA,EAAA,cAAC,EAAAa,OAAgB,MAAhB,CAAsB,YAAaM,GAAcT,CAAS,CAC7D,EACAV,EAAA,cAAC,EAAAa,OAAgB,KAAhB,CAAqB,QAAO,IAC3Bb,EAAA,cAAC,mBAAgB,UAAU,gBAAgB,CAC7C,CACF,CACF,CAEJ,CACF,EACAF,EAAc,YAAc,iBAS5B,MAAMN,EAAgBQ,EAAM,WAC1B,CAACS,EAAOK,IAAiB,CACvB,MAAMC,EAAUf,EAAM,WAAWQ,CAAa,EACxC,CAAE,UAAAQ,EAAW,SAAAN,EAAU,MAAAO,EAAO,UAAAK,EAAW,GAAGC,CAAa,KAAI,gBAEjE,CAAE,KAAMR,GAAS,KAAM,GAAGN,CAAM,EAEhC,CAAE,KAAM,qBAAmB,IAAK,EAChC,uBACF,EACMe,KAAe,mBAAgB,EAC/BC,EAAgBR,GAASO,EAAa,YAC5C,OACExB,EAAA,cAAC,EAAAa,OAAgB,OAAhB,CAAuB,UAAWS,GACjCtB,EAAA,cAAC,SAAM,QAAO,IACZA,EAAA,cAAC,EAAAa,OAAgB,QAAhB,CACC,oBAAmBY,EACnB,WAAY,EACX,GAAGF,EACJ,QAAS,GACT,IAAKT,EACL,aAAW,EAAAO,SACT,CAAE,mBAAoBE,EAAa,WAAa,QAAS,EACzD,mBACAP,CACF,GAEAhB,EAAA,cAAC,EAAA0B,WAAoB,KAApB,CAAyB,KAAK,OAAO,UAAU,qBAC9C1B,EAAA,cAAC,EAAAa,OAAgB,SAAhB,CAAyB,QAAO,GAAC,UAAU,qBAC1Cb,EAAA,cAAC,EAAA0B,WAAoB,SAApB,CACC,UAAU,wBACV,MAAO,CAAE,UAAW,MAAU,GAE7BhB,CACH,CACF,EACAV,EAAA,cAAC,EAAA0B,WAAoB,UAApB,CACC,UAAU,qCACV,YAAY,YAEZ1B,EAAA,cAAC,EAAA0B,WAAoB,MAApB,CAA0B,UAAU,qBAAqB,CAC5D,CACF,CACF,CACF,CACF,CAEJ,CACF,EACAlC,EAAc,YAAc,iBAK5B,MAAME,EAAaM,EAAM,WAA+C,CAACS,EAAOK,IAAiB,CAC/F,KAAM,CAAE,UAAAE,EAAW,SAAAN,EAAU,GAAGiB,CAAU,EAAIlB,EAC9C,OACET,EAAA,cAAC,EAAAa,OAAgB,KAAhB,CACE,GAAGc,EACJ,QAAS,GACT,IAAKb,EACL,aAAW,EAAAO,SAAW,gBAAiBL,CAAS,GAEhDhB,EAAA,cAAC,EAAAa,OAAgB,cAAhB,CAA8B,UAAU,0BACvCb,EAAA,cAAC,kBAAe,UAAU,6BAA6B,CACzD,EACAA,EAAA,cAAC,EAAAa,OAAgB,SAAhB,KAA0BH,CAAS,CACtC,CAEJ,CAAC,EACDhB,EAAW,YAAc,cAKzB,MAAMD,EAAcO,EAAM,WACxB,CAAC,CAAE,UAAAgB,EAAW,GAAGP,CAAM,EAAGK,IACxBd,EAAA,cAAC,EAAAa,OAAgB,MAAhB,CACE,GAAGJ,EACJ,QAAS,GACT,IAAKK,EACL,aAAW,EAAAO,SAAW,iBAAkBL,CAAS,EACnD,CAEJ,EACAvB,EAAY,YAAc,eAK1B,MAAME,EAAcK,EAAM,WACxB,CAAC,CAAE,UAAAgB,EAAW,GAAGP,CAAM,EAAGK,IACxBd,EAAA,cAAC,EAAAa,OAAgB,MAAhB,CACE,GAAGJ,EACJ,QAAS,GACT,IAAKK,EACL,aAAW,EAAAO,SAAW,iBAAkBL,CAAS,EACnD,CAEJ,EACArB,EAAY,YAAc,eAK1B,MAAME,EAAkBG,EAAM,WAC5B,CAAC,CAAE,UAAAgB,EAAW,GAAGP,CAAM,EAAGK,IACxBd,EAAA,cAAC,EAAAa,OAAgB,UAAhB,CACE,GAAGJ,EACJ,QAAS,GACT,IAAKK,EACL,aAAW,EAAAO,SAAW,qBAAsBL,CAAS,EACvD,CAEJ,EACAnB,EAAgB,YAAc", "names": ["select_exports", "__export", "SelectContent", "SelectGroup", "SelectItem", "SelectLabel", "SelectRoot", "SelectSeparator", "SelectTrigger", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_extract_props", "import_margin_props", "import_icons", "import_select_props", "import_theme", "SelectContext", "props", "children", "size", "rootProps", "SelectPrimitive", "forwardedRef", "context", "className", "color", "radius", "placeholder", "triggerProps", "classNames", "container", "contentProps", "themeContext", "resolvedColor", "ScrollAreaPrimitive", "itemProps"]}